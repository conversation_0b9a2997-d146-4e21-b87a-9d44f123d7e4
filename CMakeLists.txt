cmake_minimum_required(VERSION 3.20)
project(uds LANGUAGES CXX)

file(GLOB ${PROJECT_NAME}_SRC_FILES src/*.cpp src/details/*.cpp)
# file(GLOB_RECURSE ${PROJECT_NAME}_HEADER include/*.hpp)

add_library(${PROJECT_NAME}
    SHARED
        ${${PROJECT_NAME}_SRC_FILES}
)
target_include_directories(${PROJECT_NAME}
    PUBLIC
        log
        utils
        type
        concurrent
)
target_include_directories(${PROJECT_NAME}
    PUBLIC
        include
        private
)
target_link_libraries(${PROJECT_NAME}
    PUBLIC
        pthread
        log
        type
        # fmt
)

set(UDS_OPTIONS -Wno-missing-field-initializers -Wno-unused-parameter)
target_compile_options(${PROJECT_NAME} PRIVATE ${UDS_OPTIONS})

add_subdirectory(example)

# 启用测试
# enable_testing()
# add_subdirectory(test)