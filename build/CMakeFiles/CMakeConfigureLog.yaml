
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 6.8.0-60-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/local/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/3.28.3/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C"
      binary: "/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_21f41/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_21f41.dir/build.make CMakeFiles/cmTC_21f41.dir/build
        gmake[1]: Entering directory '/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C'
        Building CXX object CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o
        /usr/local/bin/c++   -v -o CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/local/bin/c++
        Target: x86_64-pc-linux-gnu
        Configured with: ../configure --enable-languages=c,c++ --disable-multilib
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.2.0 (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_21f41.dir/'
         /usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_21f41.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o /tmp/ccAgliO3.s
        GNU C++17 (GCC) version 13.2.0 (x86_64-pc-linux-gnu)
        	compiled by GNU C version 13.2.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version none
        warning: GMP header version 6.2.0 differs from library version 6.3.0.
        warning: MPFR header version 4.0.2 differs from library version 4.2.1.
        warning: MPC header version 1.1.0 differs from library version 1.3.1.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../x86_64-pc-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0
         /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/x86_64-pc-linux-gnu
         /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/backward
         /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include
         /usr/local/include
         /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed/x86_64-linux-gnu
         /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        Compiler executable checksum: 71865c2a5b29e09843a9711e354e472e
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_21f41.dir/'
         as -v --64 -o CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccAgliO3.s
        GNU assembler version 2.42 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.42
        COMPILER_PATH=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/
        LIBRARY_PATH=/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64/:/lib/x86_64-linux-gnu/:/lib/../lib64/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib64/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_21f41
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_21f41.dir/link.txt --verbose=1
        /usr/local/bin/c++  -v CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_21f41 
        Using built-in specs.
        COLLECT_GCC=/usr/local/bin/c++
        COLLECT_LTO_WRAPPER=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/lto-wrapper
        Target: x86_64-pc-linux-gnu
        Configured with: ../configure --enable-languages=c,c++ --disable-multilib
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.2.0 (GCC) 
        COMPILER_PATH=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/
        LIBRARY_PATH=/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64/:/lib/x86_64-linux-gnu/:/lib/../lib64/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib64/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_21f41' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_21f41.'
         /usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/collect2 -plugin /usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/liblto_plugin.so -plugin-opt=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccPwOfax.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_21f41 /lib/x86_64-linux-gnu/crt1.o /lib/x86_64-linux-gnu/crti.o /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtbegin.o -L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0 -L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../.. CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtend.o /lib/x86_64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_21f41' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_21f41.'
        gmake[1]: Leaving directory '/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0]
          add: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/x86_64-pc-linux-gnu]
          add: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/backward]
          add: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include]
          add: [/usr/local/include]
          add: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed/x86_64-linux-gnu]
          add: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0] ==> [/usr/local/include/c++/13.2.0]
        collapse include dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/x86_64-pc-linux-gnu] ==> [/usr/local/include/c++/13.2.0/x86_64-pc-linux-gnu]
        collapse include dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/backward] ==> [/usr/local/include/c++/13.2.0/backward]
        collapse include dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include] ==> [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed/x86_64-linux-gnu] ==> [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed/x86_64-linux-gnu]
        collapse include dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed] ==> [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/local/include/c++/13.2.0;/usr/local/include/c++/13.2.0/x86_64-pc-linux-gnu;/usr/local/include/c++/13.2.0/backward;/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include;/usr/local/include;/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed/x86_64-linux-gnu;/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_21f41/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_21f41.dir/build.make CMakeFiles/cmTC_21f41.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/Downloads/uds-test-5.28/uds/build/CMakeFiles/CMakeScratch/TryCompile-dvuz0C']
        ignore line: [Building CXX object CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/local/bin/c++   -v -o CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/local/bin/c++]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Configured with: ../configure --enable-languages=c c++ --disable-multilib]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.2.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_21f41.dir/']
        ignore line: [ /usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_21f41.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o /tmp/ccAgliO3.s]
        ignore line: [GNU C++17 (GCC) version 13.2.0 (x86_64-pc-linux-gnu)]
        ignore line: [	compiled by GNU C version 13.2.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version none]
        ignore line: [warning: GMP header version 6.2.0 differs from library version 6.3.0.]
        ignore line: [warning: MPFR header version 4.0.2 differs from library version 4.2.1.]
        ignore line: [warning: MPC header version 1.1.0 differs from library version 1.3.1.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../x86_64-pc-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0]
        ignore line: [ /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/x86_64-pc-linux-gnu]
        ignore line: [ /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../include/c++/13.2.0/backward]
        ignore line: [ /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed/x86_64-linux-gnu]
        ignore line: [ /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/include-fixed]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 71865c2a5b29e09843a9711e354e472e]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_21f41.dir/']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccAgliO3.s]
        ignore line: [GNU assembler version 2.42 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.42]
        ignore line: [COMPILER_PATH=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64/:/lib/x86_64-linux-gnu/:/lib/../lib64/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib64/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_21f41]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_21f41.dir/link.txt --verbose=1]
        ignore line: [/usr/local/bin/c++  -v CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_21f41 ]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/local/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/lto-wrapper]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Configured with: ../configure --enable-languages=c c++ --disable-multilib]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.2.0 (GCC) ]
        ignore line: [COMPILER_PATH=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/libexec/gcc/x86_64-pc-linux-gnu/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64/:/lib/x86_64-linux-gnu/:/lib/../lib64/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib64/:/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_21f41' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_21f41.']
        link line: [ /usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/collect2 -plugin /usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/liblto_plugin.so -plugin-opt=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccPwOfax.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_21f41 /lib/x86_64-linux-gnu/crt1.o /lib/x86_64-linux-gnu/crti.o /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtbegin.o -L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0 -L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../.. CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtend.o /lib/x86_64-linux-gnu/crtn.o]
          arg [/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/local/libexec/gcc/x86_64-pc-linux-gnu/13.2.0/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccPwOfax.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_21f41] ==> ignore
          arg [/lib/x86_64-linux-gnu/crt1.o] ==> obj [/lib/x86_64-linux-gnu/crt1.o]
          arg [/lib/x86_64-linux-gnu/crti.o] ==> obj [/lib/x86_64-linux-gnu/crti.o]
          arg [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtbegin.o] ==> obj [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtbegin.o]
          arg [-L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0] ==> dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0]
          arg [-L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64] ==> dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../..] ==> dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../..]
          arg [CMakeFiles/cmTC_21f41.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtend.o] ==> obj [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtend.o]
          arg [/lib/x86_64-linux-gnu/crtn.o] ==> obj [/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0] ==> [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0]
        collapse library dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../../../lib64] ==> [/usr/local/lib64]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/../../..] ==> [/usr/local/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/lib/x86_64-linux-gnu/crt1.o;/lib/x86_64-linux-gnu/crti.o;/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtbegin.o;/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0/crtend.o;/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/local/lib/gcc/x86_64-pc-linux-gnu/13.2.0;/usr/local/lib64;/lib/x86_64-linux-gnu;/lib64;/usr/lib/x86_64-linux-gnu;/usr/lib64;/usr/local/lib]
        implicit fwks: []
      
      
...
