#include <iostream>
#include <chrono>
#include <thread>
#include <unistd.h>
#include <sys/wait.h>

#include "test_message.hpp"
#include "uds/uds_client.hpp"
#include "uds/uds_server.hpp"
#include "log/log.hpp"

using TestRequest = ws::uds::TestRequest;
using TestResponse = ws::uds::TestResponse;
static constexpr char const* kLogTag = "test_timeout_consistency";

void run_server() {
    ws::uds::UdsServer<TestRequest, TestResponse> server{"test_timeout_consistency_uds"};
    
    // 添加权限白名单
    server.AddUidAuthWhiteList(getuid());
    server.AddUidAuthWhiteList(0);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    server.AsyncResponses([](TestRequest const& request, TestResponse& response) {
        std::cout << "[服务端] 收到请求 ID: " << request.id << std::endl;
        
        if (request.id == 1) {
            // 第一个请求：处理3秒（客户端会超时）
            std::cout << "[服务端] 处理第一个请求，模拟3秒处理时间..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(3));
            response.id = 1001;  // 第一个请求的响应ID
            response.status = 100;  // 第一个请求的特殊状态码
            std::cout << "[服务端] 第一个请求处理完成，响应 ID: " << response.id 
                      << ", status: " << response.status << std::endl;
        } else if (request.id == 2) {
            // 第二个请求：立即处理
            std::cout << "[服务端] 处理第二个请求，立即响应..." << std::endl;
            response.id = 2002;  // 第二个请求的响应ID
            response.status = 200;  // 第二个请求的特殊状态码
            std::cout << "[服务端] 第二个请求处理完成，响应 ID: " << response.id 
                      << ", status: " << response.status << std::endl;
        }
        
        return ws::uds::kUserRequestSucc;
    });
    
    // 保持服务端运行
    std::this_thread::sleep_for(std::chrono::seconds(10));
}

void run_client() {
    // 等待服务端启动
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    ws::uds::UdsClient<TestRequest, TestResponse> client{"test_timeout_consistency_uds"};
    
    std::cout << "\n=== 超时一致性测试开始 ===" << std::endl;
    std::cout << "测试目标：验证接收超时后，后续请求能正确接收到对应的响应" << std::endl;
    
    // 第一次请求：会超时
    std::cout << "\n--- 第一次请求（预期超时）---" << std::endl;
    TestRequest request1;
    request1.id = 1;
    
    TestResponse response1;
    auto timeout_1s = std::chrono::seconds(1);
    
    std::cout << "[客户端] 发送第一个请求 ID: " << request1.id << "，超时时间: 1秒" << std::endl;
    auto start1 = std::chrono::steady_clock::now();
    auto status1 = client.SyncRequest(request1, response1, timeout_1s);
    auto end1 = std::chrono::steady_clock::now();
    auto duration1 = std::chrono::duration_cast<std::chrono::milliseconds>(end1 - start1);
    
    std::cout << "[客户端] 第一次请求结果: " << static_cast<int>(status1) 
              << " (0=成功, 其他=失败), 耗时: " << duration1.count() << "ms" << std::endl;
    
    if (status1 == ws::uds::RequestStatus::kSendAndReceiveSuccess) {
        std::cout << "[客户端] 意外！第一次请求成功了，响应 ID: " << response1.id 
                  << ", status: " << response1.status << std::endl;
    } else {
        std::cout << "[客户端] 符合预期：第一次请求超时失败" << std::endl;
    }
    
    // 等待一段时间，确保第一个请求的响应已经发送到socket缓冲区
    std::cout << "[客户端] 等待500ms，确保第一个请求的响应已发送..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 第二次请求：不会超时
    std::cout << "\n--- 第二次请求（预期成功）---" << std::endl;
    TestRequest request2;
    request2.id = 2;
    
    TestResponse response2;
    auto timeout_5s = std::chrono::seconds(5);
    
    std::cout << "[客户端] 发送第二个请求 ID: " << request2.id << "，超时时间: 5秒" << std::endl;
    auto start2 = std::chrono::steady_clock::now();
    auto status2 = client.SyncRequest(request2, response2, timeout_5s);
    auto end2 = std::chrono::steady_clock::now();
    auto duration2 = std::chrono::duration_cast<std::chrono::milliseconds>(end2 - start2);
    
    std::cout << "[客户端] 第二次请求结果: " << static_cast<int>(status2) 
              << " (0=成功, 其他=失败), 耗时: " << duration2.count() << "ms" << std::endl;
    
    // 验证结果
    std::cout << "\n=== 结果验证 ===" << std::endl;
    if (status2 == ws::uds::RequestStatus::kSendAndReceiveSuccess) {
        std::cout << "[验证] 第二次请求成功！" << std::endl;
        std::cout << "[验证] 接收到的响应 ID: " << response2.id << std::endl;
        std::cout << "[验证] 接收到的响应 status: " << response2.status << std::endl;
        
        if (response2.status == 200 && response2.id == 2002) {
            std::cout << "\n✅ 测试通过！" << std::endl;
            std::cout << "第二次接收到的是正确的响应（status=200, ID=2002，对应第二个请求）" << std::endl;
            std::cout << "这证明了接收超时后重连机制正确清理了socket状态" << std::endl;
        } else if (response2.status == 100 && response2.id == 1001) {
            std::cout << "\n❌ 测试失败！" << std::endl;
            std::cout << "第二次接收到的是第一个请求的响应（status=100, ID=1001）" << std::endl;
            std::cout << "这表明socket缓冲区中有残留的第一次请求的响应数据" << std::endl;
        } else {
            std::cout << "\n❓ 意外的响应: status=" << response2.status 
                      << ", ID=" << response2.id << std::endl;
        }
    } else {
        std::cout << "❌ 第二次请求也失败了，无法验证数据一致性" << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
}

int main() {
    std::cout << "启动超时一致性测试..." << std::endl;
    
    pid_t pid = fork();
    
    if (pid == 0) {
        // 子进程：运行服务端
        run_server();
        exit(0);
    } else if (pid > 0) {
        // 父进程：运行客户端
        run_client();
        
        // 等待子进程结束
        int status;
        waitpid(pid, &status, 0);
    } else {
        std::cerr << "fork() 失败" << std::endl;
        return 1;
    }
    
    return 0;
}
