#include <chrono>
#include <thread>
#include <iostream>

#include "timeout_consistency_test.hpp"
#include "uds/uds_client.hpp"
#include "log/log.hpp"

using TimeoutTestRequest = ws::uds::TimeoutTestRequest;
using TimeoutTestResponse = ws::uds::TimeoutTestResponse;
static constexpr char const* kLogTag = "timeout_consistency_client";

void print_separator(const std::string& title) {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << title << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

int main() {
    print_separator("超时一致性测试客户端");
    
    ws::uds::UdsClient<TimeoutTestRequest, TimeoutTestResponse> client{"timeout_test_uds"};
    
    // 等待服务端启动
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    print_separator("测试场景：验证接收超时后的数据一致性");
    std::cout << "1. 发送第一个请求（需要处理3秒），设置1秒超时 -> 预期超时失败" << std::endl;
    std::cout << "2. 发送第二个请求（需要处理0秒），设置5秒超时 -> 预期成功" << std::endl;
    std::cout << "3. 验证第二次接收到的是第二个请求的响应，而不是第一个请求的响应" << std::endl;
    
    // 第一次请求：会超时
    print_separator("第一次请求 - 预期超时");
    TimeoutTestRequest request1;
    request1.request_id = 1001;
    request1.request_data = "FIRST_REQUEST_DATA";
    request1.processing_time_seconds = 3;  // 服务端需要处理3秒
    
    TimeoutTestResponse response1;
    auto timeout_1s = std::chrono::seconds(1);  // 1秒超时
    
    std::cout << "发送请求1: ID=" << request1.request_id 
              << ", 数据='" << request1.request_data 
              << "', 处理时间=" << request1.processing_time_seconds << "秒" << std::endl;
    std::cout << "设置超时时间: 1秒" << std::endl;
    
    auto start_time1 = std::chrono::steady_clock::now();
    auto status1 = client.SyncRequest(request1, response1, timeout_1s);
    auto end_time1 = std::chrono::steady_clock::now();
    auto duration1 = std::chrono::duration_cast<std::chrono::milliseconds>(end_time1 - start_time1);
    
    std::cout << "第一次请求结果: " << static_cast<int>(status1) 
              << " (0=成功, 其他=失败), 耗时: " << duration1.count() << "ms" << std::endl;
    
    if (status1 == ws::uds::RequestStatus::kSendAndReceiveSuccess) {
        std::cout << "意外！第一次请求成功了，响应ID: " << response1.request_id 
                  << ", 数据: '" << response1.response_data << "'" << std::endl;
    } else {
        std::cout << "符合预期：第一次请求超时失败" << std::endl;
    }
    
    // 等待一小段时间，确保第一个请求的响应已经发送到socket缓冲区
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 第二次请求：不会超时
    print_separator("第二次请求 - 预期成功");
    TimeoutTestRequest request2;
    request2.request_id = 2002;
    request2.request_data = "SECOND_REQUEST_DATA";
    request2.processing_time_seconds = 0;  // 服务端立即处理
    
    TimeoutTestResponse response2;
    auto timeout_5s = std::chrono::seconds(5);  // 5秒超时
    
    std::cout << "发送请求2: ID=" << request2.request_id 
              << ", 数据='" << request2.request_data 
              << "', 处理时间=" << request2.processing_time_seconds << "秒" << std::endl;
    std::cout << "设置超时时间: 5秒" << std::endl;
    
    auto start_time2 = std::chrono::steady_clock::now();
    auto status2 = client.SyncRequest(request2, response2, timeout_5s);
    auto end_time2 = std::chrono::steady_clock::now();
    auto duration2 = std::chrono::duration_cast<std::chrono::milliseconds>(end_time2 - start_time2);
    
    std::cout << "第二次请求结果: " << static_cast<int>(status2) 
              << " (0=成功, 其他=失败), 耗时: " << duration2.count() << "ms" << std::endl;
    
    // 验证结果
    print_separator("结果验证");
    if (status2 == ws::uds::RequestStatus::kSendAndReceiveSuccess) {
        std::cout << "第二次请求成功！" << std::endl;
        std::cout << "接收到的响应ID: " << response2.request_id << std::endl;
        std::cout << "接收到的响应数据: '" << response2.response_data << "'" << std::endl;
        std::cout << "服务端时间戳: " << response2.server_timestamp << std::endl;
        
        if (response2.request_id == request2.request_id) {
            std::cout << "\n✅ 测试通过！第二次接收到的是正确的响应（请求ID匹配）" << std::endl;
            std::cout << "这证明了接收超时后重连机制正确清理了socket状态" << std::endl;
        } else {
            std::cout << "\n❌ 测试失败！第二次接收到的响应ID不匹配" << std::endl;
            std::cout << "预期响应ID: " << request2.request_id << std::endl;
            std::cout << "实际响应ID: " << response2.request_id << std::endl;
            std::cout << "这可能表明socket缓冲区中有残留的第一次请求的响应数据" << std::endl;
        }
    } else {
        std::cout << "❌ 第二次请求也失败了，无法验证数据一致性" << std::endl;
    }
    
    print_separator("测试完成");
    
    return 0;
}
