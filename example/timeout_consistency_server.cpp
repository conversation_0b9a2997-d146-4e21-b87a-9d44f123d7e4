#include <chrono>
#include <iostream>
#include <thread>

#include "log/log.hpp"
#include "timeout_consistency_test.hpp"
#include "uds/uds_server.hpp"

using TimeoutTestRequest = ws::uds::TimeoutTestRequest;
using TimeoutTestResponse = ws::uds::TimeoutTestResponse;
static constexpr char const* kLogTag = "timeout_consistency_server";

int main() {
    std::cout << "=== 超时一致性测试服务端 ===" << std::endl;
    std::cout << "服务端将根据请求中的processing_time_seconds字段模拟不同的处理时间" << std::endl;

    ws::uds::UdsServer<TimeoutTestRequest, TimeoutTestResponse> server{"timeout_test_uds"};

    // 添加权限白名单
    uid_t const allowed_uid = getuid();  // 允许当前用户
    server.AddUidAuthWhiteList(allowed_uid);
    server.AddUidAuthWhiteList(0);  // 允许root用户

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    server.AsyncResponses([](TimeoutTestRequest const& request, TimeoutTestResponse& response) {
        auto start_time = std::chrono::steady_clock::now();
        auto timestamp =
            std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
                .count();

        ws::log::InfoFmt(kLogTag, "收到请求 ID: {}, 数据: '{}', 需要处理 {} 秒", request.request_id,
                         request.request_data, request.processing_time_seconds);

        // 模拟处理时间
        if (request.processing_time_seconds > 0) {
            std::this_thread::sleep_for(std::chrono::seconds(request.processing_time_seconds));
        }

        // 构造响应
        response.request_id = request.request_id;
        response.response_data =
            "Response for request " + std::to_string(request.request_id) + ": " + request.request_data;
        response.server_timestamp = timestamp;

        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        ws::log::InfoFmt(kLogTag, "完成请求 ID: {}, 实际处理时间: {} ms, 响应数据: '{}'", request.request_id,
                         duration.count(), response.response_data);

        return ws::uds::kUserRequestSucc;
    });

    std::cout << "服务端已启动，等待客户端连接..." << std::endl;

    // 保持服务端运行
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
