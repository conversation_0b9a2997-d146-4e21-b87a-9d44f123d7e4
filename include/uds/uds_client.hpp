#ifndef UDS_CLIENT_HPP_
#define UDS_CLIENT_HPP_

#include <cassert>
#include <chrono>
#include <memory>
#include <mutex>

#include "concurrent/thread_pool.hpp"
#include "log/log.hpp"
#include "type/serializer.hpp"
#include "uds/uds_define.hpp"
#include "utils/macros.hpp"

namespace ws::uds {

// Forward declaration
class UdsClientImpl;

class UdsClientTypeless {
public:
    explicit UdsClientTypeless(std::string const& uds_name);
    ~UdsClientTypeless();
    DISABLE_COPY_AND_MOVE(UdsClientTypeless)

    RequestStatus SyncRequest(type::Buffer const& request_buffer, type::Buffer& response_buffer,
                              std::chrono::nanoseconds const& expire = std::chrono::nanoseconds::zero());

private:
    std::unique_ptr<UdsClientImpl> pimpl_;
};

template <typename RequestType, typename ResponseType>
class UdsClient {
public:
    using onResponseCallback = std::function<void(RequestStatus const&, ResponseType const&)>;

    /**
     * \brief Construct a new UdsClient object.
     *
     * \param [dir] uds_name The path of the unix domain socket.
     */
    explicit UdsClient(std::string const& uds_name)
        : uds_name_{uds_name}, typeless_{std::make_unique<UdsClientTypeless>(uds_name)} {
        assert(!uds_name_.empty());
    }

    /**
     * \brief Destroy the UdsClient object.
     *
     */
    ~UdsClient() noexcept = default;

    /**
     * \brief  Send request to server and wait for the response synchronously.
     *
     * \param [dir] request     The request to be sent to Server.
     * \param [dir] response    The response data of request.
     * \param [dir] expire      The timeout in nanoseconds for the request.
     * @return RequestStatus    The returned request status.
     */
    RequestStatus SyncRequest(RequestType const& request, ResponseType& response,
                              std::chrono::nanoseconds const& expire = std::chrono::nanoseconds::zero()) {
        type::Serializer<RequestType> request_serializer;
        // 对应一块序列化后的内存
        type::Buffer request_buffer;
        if (!request_serializer.ToBuffer(request, request_buffer)) {
            log::ErrorFmt(kLogTag, "client sync request serialize request fail. uds_name: {}", uds_name_);
            return RequestStatus::kSerializationFail;
        }

        // 对应一块反序列化后的内存
        type::Buffer response_buffer;
        RequestStatus const status = typeless_->SyncRequest(request_buffer, response_buffer, expire);

        if (status == RequestStatus::kSendAndReceiveSuccess) {
            type::Serializer<ResponseType> response_serializer;
            if (!response_serializer.FromBuffer(response_buffer, response)) {
                log::ErrorFmt(kLogTag, "client sync request serialize response fail. uds_name: {}", uds_name_);
                return RequestStatus::kDeserializationFail;
            }
        }

        return status;
    }

    /**
     * \brief  Send request to server and wait for the response asynchronously.
     *
     * \param [dir] request     The request to be sent to Server.
     * \param [dir] callback    The callback function for request handling.
     */
    void AsyncRequest(RequestType&& request, onResponseCallback&& callback) {
        // 线程安全的懒初始化
        std::call_once(tp_init_flag_, [this]() {
            static constexpr uint64_t kDefaultWorkerCount{1UL};
            tp_ = std::make_unique<concurrent::ThreadPool>(kDefaultWorkerCount);
        });

        tp_->enqueue([this, request = std::move(request), callback = std::move(callback)]() mutable {
            ResponseType response{};
            RequestStatus const status = SyncRequest(request, response);
            callback(status, response);
        });
    }

private:
    std::string uds_name_{};
    std::unique_ptr<UdsClientTypeless> typeless_{nullptr};
    std::unique_ptr<concurrent::ThreadPool> tp_{nullptr};
    std::once_flag tp_init_flag_;  // 线程池初始化标志

    static constexpr char const* kLogTag{"uds-client"};
};

}  // namespace ws::uds

#endif  // UDS_CLIENT_HPP_