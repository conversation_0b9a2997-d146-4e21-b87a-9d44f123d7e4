#ifndef UDS_DEFINE_HPP_
#define UDS_DEFINE_HPP_

#include <cstdint>

#include "type/serializable.hpp"

namespace ws::uds {

enum class RequestStatus : uint8_t {
    kSendAndReceiveSuccess = 1U,
    kSendRequestFail = 2U,
    kReceiveResponseFail = 3U,
    kConnectionFail = 4U,
    kTimeout = 5U,          // 预留
    kMessageTooLarge = 6U,  // 预留
    kSerializationFail = 7U,
    kDeserializationFail = 8U
};

using BufferPtr = std::unique_ptr<type::Buffer>;

constexpr int32_t kUserRequestSucc = 0;
constexpr int32_t kUserRequestFail = -1;

}  // namespace ws::uds

#endif  // UDS_DEFINE_HPP_