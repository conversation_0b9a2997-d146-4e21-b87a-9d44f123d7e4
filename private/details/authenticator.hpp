#ifndef UDS_AUTH_HPP_
#define UDS_AUTH_HPP_

#include <sys/types.h>

#include <memory>
#include <unordered_set>

#include "details/socket_client.hpp"
#include "utils/macros.hpp"
#include "utils/mutex_helper.hpp"

namespace ws::uds {

class Authenticator {
public:
    Authenticator() = default;
    ~Authenticator() = default;
    DISABLE_COPY_AND_MOVE(Authenticator)

    void AddUidAuthWhiteList(uid_t const uid);
    void AddGidAuthWhiteList(gid_t const gid);
    void AddAuthWhiteList(uid_t const uid, gid_t const gid);

    bool VerifyClient(SocketClient const* const client);

private:
    utils::MutexHelper<std::unordered_set<uid_t>> allowed_uids_;
    utils::MutexHelper<std::unordered_set<gid_t>> allowed_gids_;

    static constexpr char const* kLogTag{"uds-authenticator"};
};

}  // namespace ws::uds

#endif  // UDS_AUTH_HPP_