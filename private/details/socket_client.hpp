#ifndef UDS_SOCKET_CLIENT_HPP_
#define UDS_SOCKET_CLIENT_HPP_

#include <sys/types.h>
#include <sys/uio.h>

#include <string>

namespace ws::uds {

class SocketClient {
public:
    explicit SocketClient(int32_t const sock_fd);
    ~SocketClient() noexcept = default;

    [[nodiscard]]
    int32_t GetSocket() const {
        return sock_fd_;
    }

    [[nodiscard]]
    pid_t GetPid() const {
        return pid_;
    }

    [[nodiscard]]
    uid_t GetUid() const {
        return uid_;
    }

    [[nodiscard]]
    gid_t GetGid() const {
        return gid_;
    }

    [[nodiscard]]
    uint32_t GetMaxSendSize() const {
        return max_send_size_;
    }

    [[nodiscard]]
    uint32_t GetMaxReceiveSize() const {
        return max_receive_size_;
    }

    bool GetClientCredentials();
    bool GetSocketBufferSizes();

private:
    int32_t sock_fd_{-1};

    pid_t pid_{0};
    uid_t uid_{0};
    gid_t gid_{0};

    uint32_t max_send_size_{0};
    uint32_t max_receive_size_{0};

    static constexpr char const* kLogTag{"uds-socketClient"};
};

}  // namespace ws::uds

#endif  // UDS_SOCKET_CLIENT_HPP_