#ifndef UDS_SOCKET_UTILS_HPP_
#define UDS_SOCKET_UTILS_HPP_

#include <sys/socket.h>

#include <chrono>
#include <cstdint>

namespace ws::uds {

/**
 * @brief Socket缓冲区大小信息
 */
struct SocketBufferSizes {
    uint32_t send_buffer_size{0};     ///< 发送缓冲区大小
    uint32_t receive_buffer_size{0};  ///< 接收缓冲区大小
};

/**
 * @brief Socket工具类，提供通用的socket操作函数
 */
class SocketUtils {
public:
    /**
     * @brief 获取socket的发送和接收缓冲区大小
     * @param sock_fd socket文件描述符
     * @param buffer_sizes 输出参数，存储缓冲区大小信息
     * @return 成功返回true，失败返回false
     */
    static bool GetSocketBufferSizes(int32_t sock_fd, SocketBufferSizes& buffer_sizes) noexcept;

    /**
     * @brief 设置socket的发送和接收超时时间
     * @param sock_fd socket文件描述符
     * @param timeout 超时时间，0表示无超时
     * @return 成功返回true，失败返回false
     */
    static bool SetSocketTimeout(int32_t sock_fd, std::chrono::nanoseconds timeout) noexcept;

    /**
     * @brief 验证消息大小是否在允许范围内
     * @param message_size 消息大小
     * @param max_size 最大允许大小
     * @param operation_name 操作名称（用于日志）
     * @return 验证通过返回true，否则返回false
     */
    static bool ValidateMessageSize(uint32_t message_size, uint32_t max_size, const char* operation_name) noexcept;

    /**
     * @brief 计算有效的最大消息大小
     * @param buffer_size socket缓冲区大小
     * @return 有效的最大消息大小
     * @note 考虑到协议头部开销，实际可用大小会略小于缓冲区大小
     */
    static uint32_t CalculateMaxMessageSize(uint32_t buffer_size) noexcept;

    // 协议开销：消息长度字段(4字节) + 一些系统开销
    static constexpr uint32_t kProtocolOverhead{64};

    // 默认最大消息大小（当无法获取socket缓冲区大小时使用）
    static constexpr uint32_t kDefaultMaxMessageSize{1024 * 1024};  // 1MB

private:
    static constexpr char const* kLogTag{"socket-utils"};
};

}  // namespace ws::uds

#endif  // UDS_SOCKET_UTILS_HPP_
