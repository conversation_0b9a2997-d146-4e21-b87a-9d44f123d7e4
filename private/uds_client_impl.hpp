#ifndef UDS_CLIENT_IMPL_HPP_
#define UDS_CLIENT_IMPL_HPP_

#include <chrono>
#include <string>

#include "uds/uds_define.hpp"
#include "utils/macros.hpp"

namespace ws::uds {
class UdsClientImpl {
public:
    explicit UdsClientImpl(std::string uds_name);
    ~UdsClientImpl();
    DISABLE_COPY_AND_MOVE(UdsClientImpl)

    [[nodiscard]]
    std::string GetUdsPath() const {
        return uds_name_;
    }

    RequestStatus SyncRequest(type::Buffer const& request_buffer, type::Buffer& response_buffer,
                              std::chrono::nanoseconds const& expire = std::chrono::nanoseconds::zero());

private:
    void Connect();
    void CloseConnect();
    void ReConnect();

    bool SetSocketTimeout(std::chrono::nanoseconds const& expire);
    bool SendRequest(type::<PERSON><PERSON><PERSON> const& request_buffer);
    bool ReceiveResponse(type::Buffer& response_buffer);
    bool InitializeSocketBufferSizes();

    std::string uds_name_{};
    int32_t sock_fd_{-1};
    bool is_connected_{false};
    uint32_t request_sequence_{0};  // 请求序列号

    uint32_t max_send_size_{0};     // 最大发送消息大小
    uint32_t max_receive_size_{0};  // 最大接收消息大小

    static constexpr char const* kLogTag{"uds-client-impl"};
};

}  // namespace ws::uds

#endif  // UDS_CLIENT_IMPL_HPP_