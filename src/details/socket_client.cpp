#include "details/socket_client.hpp"

#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

#include "details/socket_utils.hpp"
#include "log/log.hpp"

namespace ws::uds {
SocketClient::SocketClient(int32_t const sock_fd) : sock_fd_(sock_fd) {
    GetClientCredentials();
    GetSocketBufferSizes();
}

bool SocketClient::GetClientCredentials() {
    struct ucred cred{};
    socklen_t len = sizeof(cred);

    if (::getsockopt(sock_fd_, SOL_SOCKET, SO_PEERCRED, &cred, &len) == 0) {
        pid_ = cred.pid;
        uid_ = cred.uid;
        gid_ = cred.gid;
        log::DebugFmt(kLogTag, "get client credentials: uid: {}, gid: {}, pid: {}", uid_, gid_, pid_);
    }

    return true;
}

bool SocketClient::GetSocketBufferSizes() {
    SocketBufferSizes buffer_sizes;
    if (!SocketUtils::GetSocketBufferSizes(sock_fd_, buffer_sizes)) {
        log::WarnFmt(kLogTag, "Failed to get socket buffer sizes, using default values");
        max_send_size_ = SocketUtils::CalculateMaxMessageSize(SocketUtils::kDefaultMaxMessageSize);
        max_receive_size_ = SocketUtils::CalculateMaxMessageSize(SocketUtils::kDefaultMaxMessageSize);
        return false;
    }

    max_send_size_ = SocketUtils::CalculateMaxMessageSize(buffer_sizes.send_buffer_size);
    max_receive_size_ = SocketUtils::CalculateMaxMessageSize(buffer_sizes.receive_buffer_size);

    log::DebugFmt(kLogTag, "Client socket max message sizes - Send: {} bytes, Receive: {} bytes", max_send_size_,
                  max_receive_size_);

    return true;
}

}  // namespace ws::uds