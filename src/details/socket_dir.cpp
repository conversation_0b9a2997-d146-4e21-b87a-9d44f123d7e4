#include "details/socket_dir.hpp"

#include <filesystem>

#include "log/log.hpp"

namespace ws::uds {
bool CreateSocketDir(std::string const& dir) {
    try {
        if (!std::filesystem::exists(dir)) {
            std::filesystem::create_directory(dir);
        }
    } catch (std::exception const& e) {
        log::ErrorFmt("uds-socket-dir", "create socket dir fail. dir: {}, error: {}", dir, e.what());
        return false;
    }
    return true;
}

}  // namespace ws::uds