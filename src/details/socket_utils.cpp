#include "details/socket_utils.hpp"

#include <sys/socket.h>
#include <cerrno>
#include <cstring>
#include <algorithm>

#include "log/log.hpp"

namespace ws::uds {

bool SocketUtils::GetSocketBufferSizes(int32_t sock_fd, SocketBufferSizes& buffer_sizes) noexcept {
    if (sock_fd < 0) {
        log::ErrorFmt(kLogTag, "Invalid socket file descriptor: {}", sock_fd);
        return false;
    }

    // 获取发送缓冲区大小
    int send_buffer_size = 0;
    socklen_t optlen = sizeof(send_buffer_size);
    if (::getsockopt(sock_fd, SOL_SOCKET, SO_SNDBUF, &send_buffer_size, &optlen) < 0) {
        log::ErrorFmt(kLogTag, "Failed to get SO_SNDBUF: {}", strerror(errno));
        return false;
    }

    // 获取接收缓冲区大小
    int receive_buffer_size = 0;
    optlen = sizeof(receive_buffer_size);
    if (::getsockopt(sock_fd, SOL_SOCKET, SO_RCVBUF, &receive_buffer_size, &optlen) < 0) {
        log::ErrorFmt(kLogTag, "Failed to get SO_RCVBUF: {}", strerror(errno));
        return false;
    }

    buffer_sizes.send_buffer_size = static_cast<uint32_t>(std::max(0, send_buffer_size));
    buffer_sizes.receive_buffer_size = static_cast<uint32_t>(std::max(0, receive_buffer_size));

    log::DebugFmt(kLogTag, "Socket buffer sizes - Send: {} bytes, Receive: {} bytes", 
                  buffer_sizes.send_buffer_size, buffer_sizes.receive_buffer_size);

    return true;
}

bool SocketUtils::SetSocketTimeout(int32_t sock_fd, std::chrono::nanoseconds timeout) noexcept {
    if (sock_fd < 0) {
        log::ErrorFmt(kLogTag, "Invalid socket file descriptor: {}", sock_fd);
        return false;
    }

    struct timeval tv;
    if (timeout.count() <= 0) {
        // 设置为0表示无超时（阻塞模式）
        tv.tv_sec = 0;
        tv.tv_usec = 0;
    } else {
        // 将纳秒转换为秒和微秒
        auto seconds = std::chrono::duration_cast<std::chrono::seconds>(timeout);
        auto remaining_ns = timeout - seconds;
        auto microseconds = std::chrono::duration_cast<std::chrono::microseconds>(remaining_ns);

        tv.tv_sec = static_cast<time_t>(seconds.count());
        tv.tv_usec = static_cast<suseconds_t>(microseconds.count());

        // 确保至少有1微秒的超时，避免立即超时
        if (tv.tv_sec == 0 && tv.tv_usec == 0) {
            tv.tv_usec = 1;
        }
    }

    // 设置发送超时
    if (::setsockopt(sock_fd, SOL_SOCKET, SO_SNDTIMEO, &tv, sizeof(tv)) < 0) {
        log::ErrorFmt(kLogTag, "Failed to set SO_SNDTIMEO: {}", strerror(errno));
        return false;
    }

    // 设置接收超时
    if (::setsockopt(sock_fd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv)) < 0) {
        log::ErrorFmt(kLogTag, "Failed to set SO_RCVTIMEO: {}", strerror(errno));
        return false;
    }

    if (timeout.count() > 0) {
        log::DebugFmt(kLogTag, "Set socket timeout: {}s {}μs", tv.tv_sec, tv.tv_usec);
    }

    return true;
}

bool SocketUtils::ValidateMessageSize(uint32_t message_size, uint32_t max_size, 
                                     const char* operation_name) noexcept {
    if (operation_name == nullptr) {
        operation_name = "unknown";
    }

    if (max_size == 0) {
        log::WarnFmt(kLogTag, "Max message size is 0 for operation: {}", operation_name);
        return false;
    }

    if (message_size > max_size) {
        log::ErrorFmt(kLogTag, "{} message too large: {} bytes (max: {} bytes)", 
                      operation_name, message_size, max_size);
        return false;
    }

    if (message_size == 0) {
        log::WarnFmt(kLogTag, "{} message is empty", operation_name);
    }

    return true;
}

uint32_t SocketUtils::CalculateMaxMessageSize(uint32_t buffer_size) noexcept {
    if (buffer_size <= kProtocolOverhead) {
        log::WarnFmt(kLogTag, "Buffer size {} is too small, using default max message size", 
                     buffer_size);
        return kDefaultMaxMessageSize;
    }

    uint32_t max_size = buffer_size - kProtocolOverhead;
    log::DebugFmt(kLogTag, "Calculated max message size: {} bytes (buffer: {} bytes, overhead: {} bytes)", 
                  max_size, buffer_size, kProtocolOverhead);

    return max_size;
}

}  // namespace ws::uds
