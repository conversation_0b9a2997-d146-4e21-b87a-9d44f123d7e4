#include "uds_client_impl.hpp"

#include <sys/socket.h>
#include <sys/uio.h>
#include <sys/un.h>
#include <unistd.h>

#include <algorithm>

#include "details/socket_dir.hpp"
#include "details/socket_utils.hpp"
#include "fmt/format.h"  // fmt::format()
#include "log/log.hpp"
#include "uds/uds_client.hpp"
#include "uds/uds_define.hpp"

namespace ws::uds {

UdsClientTypeless::UdsClientTypeless(std::string const& uds_name) {
    pimpl_ = std::make_unique<UdsClientImpl>(uds_name);
}

UdsClientTypeless::~UdsClientTypeless() = default;

RequestStatus UdsClientTypeless::SyncRequest(type::Buffer const& request_buffer, type::Buffer& response_buffer,
                                             std::chrono::nanoseconds const& expire) {
    return pimpl_->SyncRequest(request_buffer, response_buffer, expire);
}

///////////////////////////// uds client impl ////////////////////////////
UdsClientImpl::UdsClientImpl(std::string uds_name) : uds_name_{std::move(uds_name)} {
    sock_fd_ = TEMP_FAILURE_RETRY(::socket(AF_UNIX, SOCK_SEQPACKET, 0));
    if (sock_fd_ < 0) {
        log::ErrorFmt(kLogTag, "create socket fail. error: {}", strerror(errno));
        is_connected_ = false;
        return;
    }

    Connect();
}

UdsClientImpl::~UdsClientImpl() { CloseConnect(); }

RequestStatus UdsClientImpl::SyncRequest(type::Buffer const& request_buffer, type::Buffer& response_buffer,
                                         std::chrono::nanoseconds const& expire) {
    if (!is_connected_) {
        log::ErrorFmt(kLogTag, "server may disconnected, try to reconnect");
        ReConnect();
        if (!is_connected_) {
            log::ErrorFmt(kLogTag, "client reconnect failed, server maybe not started");
            return RequestStatus::kConnectionFail;
        }
    }

    // 设置socket超时时间
    if (!SetSocketTimeout(expire)) {
        log::ErrorFmt(kLogTag, "failed to set socket timeout");
        return RequestStatus::kConnectionFail;
    }

    if (!SendRequest(request_buffer)) {
        return RequestStatus::kSendRequestFail;
    }

    if (!ReceiveResponse(response_buffer)) {
        // 接收失败时，可能是超时或其他错误
        // 为了保证后续请求的正确性，需要重新连接以清理socket状态
        log::WarnFmt(kLogTag, "Receive response failed, will reconnect to ensure clean state for next request");
        ReConnect();
        return RequestStatus::kReceiveResponseFail;
    }

    return RequestStatus::kSendAndReceiveSuccess;
}

void UdsClientImpl::Connect() {
    struct sockaddr_un addr{};
    addr.sun_family = AF_UNIX;

    std::string const socket_path = fmt::format("{}/{}", kSocketDir, uds_name_);
    if (socket_path.size() >= sizeof(addr.sun_path) || uds_name_.find('/') != std::string::npos) {
        log::ErrorFmt(kLogTag, "socket path too long or contains illegal character: {}", socket_path);
        throw std::runtime_error("socket path too long or contains illegal character");
    }
    strncpy(addr.sun_path, socket_path.c_str(), sizeof(addr.sun_path) - 1);

    int32_t const ret =
        TEMP_FAILURE_RETRY(::connect(sock_fd_, reinterpret_cast<struct sockaddr*>(&addr), sizeof(addr)));
    if (ret < 0) {
        log::ErrorFmt(kLogTag, "connect fail. error: {}", strerror(errno));
        is_connected_ = false;
        return;
    }

    is_connected_ = true;

    // 初始化socket缓冲区大小
    InitializeSocketBufferSizes();
}

void UdsClientImpl::CloseConnect() {
    if (sock_fd_ >= 0) {
        ::close(sock_fd_);
        sock_fd_ = -1;
    }

    is_connected_ = false;
}

void UdsClientImpl::ReConnect() {
    CloseConnect();
    sock_fd_ = TEMP_FAILURE_RETRY(::socket(AF_UNIX, SOCK_SEQPACKET, 0));
    if (sock_fd_ < 0) {
        log::ErrorFmt(kLogTag, "create socket fail. error: {}", strerror(errno));
        return;
    }

    Connect();
}

bool UdsClientImpl::SendRequest(type::Buffer const& request_buffer) {
    uint32_t const request_buffer_size = request_buffer.size();

    // 检查消息大小
    if (!SocketUtils::ValidateMessageSize(request_buffer_size, max_send_size_, "Send request")) {
        return false;
    }

    std::array<struct iovec, 2UL> iov{};
    iov[0].iov_base = const_cast<void*>(static_cast<const void*>(&request_buffer_size));
    iov[0].iov_len = sizeof(request_buffer_size);
    iov[1].iov_base = const_cast<void*>(static_cast<const void*>(request_buffer.data()));
    iov[1].iov_len = request_buffer.size();

    // log::DebugFmt(kLogTag, "客户端开始发送请求");
    ssize_t const ret{TEMP_FAILURE_RETRY(::writev(sock_fd_, iov.data(), 2))};
    // log::DebugFmt(kLogTag, "客户端发送请求完毕");
    if (ret < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            // 发送超时，这是正常的超时情况
            log::ErrorFmt(kLogTag, "Send request timeout: {}", strerror(errno));
            return false;
        } else {
            // 其他错误，可能是连接断开，尝试重连
            log::ErrorFmt(kLogTag, "Server may have disconnected, first request failed: {}", strerror(errno));
            ReConnect();
            if (!is_connected_) {
                log::ErrorFmt(kLogTag, "Failed to reconnect to server");
                return false;
            }

            ssize_t const ret_retry{TEMP_FAILURE_RETRY(::writev(sock_fd_, iov.data(), 2))};
            if (ret_retry < 0) {
                log::ErrorFmt(kLogTag, "writev fail after reconnect. error: {}", strerror(errno));
                return false;
            }
        }
    }

    return true;
}

bool UdsClientImpl::SetSocketTimeout(std::chrono::nanoseconds const& expire) {
    return SocketUtils::SetSocketTimeout(sock_fd_, expire);
}

bool UdsClientImpl::ReceiveResponse(type::Buffer& response_buffer) {
    uint32_t response_data_size{0};

    std::array<struct iovec, 2UL> iov{};
    iov[0].iov_base = &response_data_size;
    iov[0].iov_len = sizeof(response_data_size);

    struct msghdr peek_msg = {
        .msg_iov = iov.data(),
        .msg_iovlen = 1,
    };

    // log::DebugFmt(kLogTag, "客户端开始接收请求");
    ssize_t const peeked_ret{::recvmsg(sock_fd_, &peek_msg, MSG_PEEK)};
    // log::DebugFmt(kLogTag, "客户端接收请求完毕");
    if (peeked_ret < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            log::ErrorFmt(kLogTag, "[ReceiveResponse] receive timeout: {}", strerror(errno));
        } else {
            log::ErrorFmt(kLogTag, "[ReceiveResponse] first recvmsg error: {}", strerror(errno));
        }
        return false;
    }

    response_buffer.resize(response_data_size);

    iov[1].iov_base = response_buffer.data();
    iov[1].iov_len = response_buffer.size();

    struct msghdr msg = {
        .msg_iov = iov.data(),
        .msg_iovlen = 2,
    };

    ssize_t const ret{::recvmsg(sock_fd_, &msg, 0)};
    if (ret < 0) {
        log::ErrorFmt(kLogTag, "recvmsg fail. error: {}", strerror(errno));
        return false;
    }

    return true;
}

bool UdsClientImpl::InitializeSocketBufferSizes() {
    SocketBufferSizes buffer_sizes;
    if (!SocketUtils::GetSocketBufferSizes(sock_fd_, buffer_sizes)) {
        log::WarnFmt(kLogTag, "Failed to get socket buffer sizes, using default values");
        max_send_size_ = SocketUtils::CalculateMaxMessageSize(SocketUtils::kDefaultMaxMessageSize);
        max_receive_size_ = SocketUtils::CalculateMaxMessageSize(SocketUtils::kDefaultMaxMessageSize);
        return false;
    }

    max_send_size_ = SocketUtils::CalculateMaxMessageSize(buffer_sizes.send_buffer_size);
    max_receive_size_ = SocketUtils::CalculateMaxMessageSize(buffer_sizes.receive_buffer_size);

    log::DebugFmt(kLogTag, "Client max message sizes - Send: {} bytes, Receive: {} bytes", max_send_size_,
                  max_receive_size_);

    return true;
}

}  // namespace ws::uds
