#ifndef UDS_TEST_HELPERS_HPP_
#define UDS_TEST_HELPERS_HPP_

#include <atomic>
#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <thread>

#include "gtest/gtest.h"
#include "log/log.hpp"
#include "test_messages.hpp"
#include "uds/uds_client.hpp"
#include "uds/uds_server.hpp"

namespace ws::uds::test {

// 测试服务器类，管理服务器的生命周期
class TestServer {
public:
    using ServerType = UdsServer<SimpleRequest, SimpleResponse>;
    using ServerCallback = std::function<int32_t(SimpleRequest const&, SimpleResponse&)>;

    explicit TestServer(std::string const& socket_path, bool enable_auth = false)
        : socket_path_(socket_path), running_(false) {
        server_ = std::make_unique<ServerType>(socket_path, enable_auth);
    }

    ~TestServer() { Stop(); }

    // 启动服务器
    void Start(ServerCallback callback) {
        if (running_) {
            return;
        }

        // 添加当前用户到白名单
        server_->AddUidAuthWhiteList(getuid());

        // 设置回调
        server_->AsyncResponses([this, callback](SimpleRequest const& request, SimpleResponse& response) {
            // 检查是否应该模拟崩溃
            if (request.command == static_cast<int32_t>(TestCommand::CRASH_SERVER)) {
                log::WarnFmt("TestServer", "Simulating server crash");
                this->Stop();
                return kUserRequestFail;
            }

            // 检查是否应该延迟
            if (request.command == static_cast<int32_t>(TestCommand::DELAY)) {
                int delay_ms = std::stoi(request.data);
                log::InfoFmt("TestServer", "Delaying response for {} ms", delay_ms);
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
            }

            // 调用用户提供的回调
            return callback(request, response);
        });

        running_ = true;
        log::InfoFmt("TestServer", "Server started on {}", socket_path_);
    }

    // 停止服务器
    void Stop() {
        if (!running_) {
            return;
        }

        server_->StopListener();
        running_ = false;
        log::InfoFmt("TestServer", "Server stopped");
    }

    // 重启服务器
    void Restart(ServerCallback callback) {
        Stop();
        // 给系统一些时间清理资源
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        server_ = std::make_unique<ServerType>(socket_path_, false);
        Start(callback);
        log::InfoFmt("TestServer", "Server restarted");
    }

    bool IsRunning() const { return running_; }

private:
    std::string socket_path_;
    std::unique_ptr<ServerType> server_;
    std::atomic<bool> running_;

    static constexpr char const* kLogTag = "TestServer";
};

// 测试客户端类，管理客户端的生命周期
class TestClient {
public:
    using ClientType = UdsClient<SimpleRequest, SimpleResponse>;

    explicit TestClient(std::string const& socket_path) : socket_path_(socket_path) {
        client_ = std::make_unique<ClientType>(socket_path);
    }

    ~TestClient() = default;

    // 发送同步请求
    RequestStatus SendRequest(SimpleRequest const& request, SimpleResponse& response) {
        return client_->SyncRequest(request, response, );
    }

    // 重新创建客户端（模拟断开重连）
    void Reconnect() {
        client_ = std::make_unique<ClientType>(socket_path_);
        log::InfoFmt("TestClient", "Client reconnected to {}", socket_path_);
    }

private:
    std::string socket_path_;
    std::unique_ptr<ClientType> client_;

    static constexpr char const* kLogTag = "TestClient";
};

// 生成唯一的套接字路径，避免测试之间的冲突
inline std::string GenerateUniqueSocketPath() {
    static int counter = 0;
    return "test_uds_" + std::to_string(getpid()) + "_" + std::to_string(counter++);
}

}  // namespace ws::uds::test

#endif  // UDS_TEST_HELPERS_HPP_
