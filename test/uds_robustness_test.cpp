#include <chrono>
#include <string>
#include <thread>
#include <vector>

#include "gtest/gtest.h"
#include "log/log.hpp"
#include "test_helpers.hpp"
#include "test_messages.hpp"
#include "uds/uds_define.hpp"

namespace ws::uds::test {

class UdsRobustnessTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志
        log::InitFmt("UdsRobustnessTest", log::Level::kDebug);
    }

    void TearDown() override {
        // 清理资源
    }
};

// 基本通信测试
TEST_F(UdsRobustnessTest, BasicCommunication) {
    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Echo: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建客户端
    TestClient client(socket_path);

    // 发送请求
    SimpleRequest request;
    request.id = 1;
    request.command = static_cast<int32_t>(TestCommand::ECHO);
    request.data = "Hello, World!";

    SimpleResponse response;
    auto status = client.SendRequest(request, response);

    // 验证结果
    EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
    EXPECT_EQ(response.id, request.id);
    EXPECT_EQ(response.status, 0);
    EXPECT_EQ(response.data, "Echo: Hello, World!");

    server.Stop();
}

// 测试客户端断开重连
TEST_F(UdsRobustnessTest, ClientReconnection) {
    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Echo: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建客户端
    TestClient client(socket_path);

    // 发送第一个请求
    {
        SimpleRequest request;
        request.id = 1;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "First request";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(response.data, "Echo: First request");
    }

    // 模拟客户端断开重连
    client.Reconnect();

    // 发送第二个请求
    {
        SimpleRequest request;
        request.id = 2;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "After reconnection";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(response.data, "Echo: After reconnection");
    }

    server.Stop();
}

// 测试服务器重启
TEST_F(UdsRobustnessTest, ServerRestart) {
    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Echo: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建客户端
    TestClient client(socket_path);

    // 发送第一个请求
    {
        SimpleRequest request;
        request.id = 1;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "Before server restart";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(response.data, "Echo: Before server restart");
    }

    // 重启服务器
    server.Restart([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Restarted: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器重启
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 客户端需要重连
    client.Reconnect();

    // 发送第二个请求
    {
        SimpleRequest request;
        request.id = 2;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "After server restart";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(response.data, "Restarted: After server restart");
    }

    server.Stop();
}

// 测试服务器崩溃
TEST_F(UdsRobustnessTest, ServerCrash) {
    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Echo: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建客户端
    TestClient client(socket_path);

    // 发送导致服务器"崩溃"的请求
    {
        SimpleRequest request;
        request.id = 1;
        request.command = static_cast<int32_t>(TestCommand::CRASH_SERVER);
        request.data = "Crash server";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        // 服务器崩溃后，客户端应该收到错误
        EXPECT_NE(status, RequestStatus::kSendAndReceiveSuccess);
    }

    // 尝试发送另一个请求，应该失败
    {
        SimpleRequest request;
        request.id = 2;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "After server crash";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        // 服务器已崩溃，应该返回连接错误
        EXPECT_NE(status, RequestStatus::kSendAndReceiveSuccess);
    }

    // 重启服务器
    server.Restart([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Recovered: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器重启
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 客户端重连
    client.Reconnect();

    // 发送恢复后的请求
    {
        SimpleRequest request;
        request.id = 3;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "After recovery";

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(response.data, "Recovered: After recovery");
    }
}

// 测试超时处理
// TEST_F(UdsRobustnessTest, Timeout) {
//     std::string socket_path = GenerateUniqueSocketPath();

//     // 创建服务器
//     TestServer server(socket_path);
//     server.Start([](SimpleRequest const& request, SimpleResponse& response) {
//         response.id = request.id;
//         response.status = 0;

//         // 如果是延迟命令，服务器会在TestServer::Start中延迟
//         if (request.command == static_cast<int32_t>(TestCommand::DELAY)) {
//             response.data = "Delayed response";
//         } else {
//             response.data = "Echo: " + request.data;
//         }

//         return kUserRequestSucc;
//     });

//     // 等待服务器启动
//     std::this_thread::sleep_for(std::chrono::milliseconds(100));

//     // 创建客户端
//     TestClient client(socket_path);

//     // 发送正常请求
//     {
//         SimpleRequest request;
//         request.id = 1;
//         request.command = static_cast<int32_t>(TestCommand::ECHO);
//         request.data = "Normal request";

//         SimpleResponse response;
//         auto status = client.SendRequest(request, response);

//         EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
//     }

//     // 发送会导致超时的请求
//     {
//         SimpleRequest request;
//         request.id = 2;
//         request.command = static_cast<int32_t>(TestCommand::DELAY);
//         request.data = "5000"; // 延迟5000毫秒

//         SimpleResponse response;
//         auto status = client.SendRequest(request, response, std::chrono::milliseconds(1000)); // 1秒超时

//         // 应该超时
//         EXPECT_EQ(status, RequestStatus::kTimeout);
//     }

//     // 发送另一个正常请求，确保客户端仍然可用
//     {
//         SimpleRequest request;
//         request.id = 3;
//         request.command = static_cast<int32_t>(TestCommand::ECHO);
//         request.data = "After timeout";

//         SimpleResponse response;
//         auto status = client.SendRequest(request, response);

//         EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
//         EXPECT_EQ(response.data, "Echo: After timeout");
//     }

//     server.Stop();
// }

// 测试多个客户端
TEST_F(UdsRobustnessTest, MultipleClients) {
    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Client " + std::to_string(request.id) + ": " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建多个客户端
    constexpr int num_clients = 5;
    std::vector<std::unique_ptr<TestClient>> clients;

    for (int i = 0; i < num_clients; ++i) {
        clients.push_back(std::make_unique<TestClient>(socket_path));
    }

    // 所有客户端同时发送请求
    std::vector<std::thread> threads;
    std::vector<RequestStatus> statuses(num_clients);
    std::vector<SimpleResponse> responses(num_clients);

    for (int i = 0; i < num_clients; ++i) {
        threads.emplace_back([&, i]() {
            SimpleRequest request;
            request.id = i + 1;
            request.command = static_cast<int32_t>(TestCommand::ECHO);
            request.data = "Hello from client " + std::to_string(i + 1);

            statuses[i] = clients[i]->SendRequest(request, responses[i]);
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 验证所有请求都成功
    for (int i = 0; i < num_clients; ++i) {
        EXPECT_EQ(statuses[i], RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(responses[i].id, i + 1);
        EXPECT_EQ(responses[i].data,
                  "Client " + std::to_string(i + 1) + ": Hello from client " + std::to_string(i + 1));
    }

    server.Stop();
}

// 测试大型消息
TEST_F(UdsRobustnessTest, LargeMessage) {
    // 使用不同的消息类型进行测试
    using LargeClient = UdsClient<LargeRequest, SimpleResponse>;

    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Received large message";
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建客户端
    auto client = std::make_unique<LargeClient>(socket_path);

    // 创建一个超过最大消息大小的请求
    LargeRequest large_request;
    large_request.id = 1;
    large_request.large_data = std::string(2 * 1024 * 1024, 'X');  // 2MB 数据

    SimpleResponse response;
    auto status = client->SyncRequest(large_request, response);

    // 应该返回消息过大错误
    EXPECT_EQ(status, RequestStatus::kMessageTooLarge);

    server.Stop();
}

// 测试连续请求
TEST_F(UdsRobustnessTest, ConsecutiveRequests) {
    std::string socket_path = GenerateUniqueSocketPath();

    // 创建服务器
    TestServer server(socket_path);
    server.Start([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Echo: " + request.data;
        return kUserRequestSucc;
    });

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 创建客户端
    TestClient client(socket_path);

    // 发送多个连续请求
    constexpr int num_requests = 100;

    for (int i = 0; i < num_requests; ++i) {
        SimpleRequest request;
        request.id = i + 1;
        request.command = static_cast<int32_t>(TestCommand::ECHO);
        request.data = "Request " + std::to_string(i + 1);

        SimpleResponse response;
        auto status = client.SendRequest(request, response);

        EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
        EXPECT_EQ(response.id, i + 1);
        EXPECT_EQ(response.data, "Echo: Request " + std::to_string(i + 1));
    }

    server.Stop();
}

}  // namespace ws::uds::test

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
