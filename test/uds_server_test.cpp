TEST_F(UdsServerTest, AsyncResponses_NormalPath) {
    // 创建服务器
    UdsServer<SimpleRequest, SimpleResponse> server(socket_path_);
    
    // 启动服务器
    server.AsyncResponses([](SimpleRequest const& request, SimpleResponse& response) {
        response.id = request.id;
        response.status = 0;
        response.data = "Success";
        return kUserRequestSucc;
    });
    
    // 创建客户端并发送请求
    TestClient client(socket_path_);
    SimpleRequest request{1, 0, "Test"};
    SimpleResponse response;
    auto status = client.SendRequest(request, response);
    
    // 验证正常路径
    EXPECT_EQ(status, RequestStatus::kSendAndReceiveSuccess);
    EXPECT_EQ(response.id, 1);
    EXPECT_EQ(response.data, "Success");
}

TEST_F(UdsServerTest, AsyncResponses_RequestDeserializationFail) {
    // 创建服务器
    UdsServer<SimpleRequest, SimpleResponse> server(socket_path_);
    
    // 启动服务器
    bool callback_called = false;
    server.AsyncResponses([&callback_called](SimpleRequest const& request, SimpleResponse& response) {
        callback_called = true;
        return kUserRequestSucc;
    });
    
    // 发送无效的请求数据
    int sock = socket(AF_UNIX, SOCK_STREAM, 0);
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);
    connect(sock, (struct sockaddr*)&addr, sizeof(addr));
    
    // 发送无效数据
    char invalid_data[] = "Invalid data";
    send(sock, invalid_data, sizeof(invalid_data), 0);
    close(sock);
    
    // 等待服务器处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 验证回调未被调用
    EXPECT_FALSE(callback_called);
}

TEST_F(UdsServerTest, AsyncResponses_ResponseSerializationFail) {
    // 创建一个特殊的响应类型，其序列化总是失败
    struct NonSerializableResponse : public SimpleResponse {
        bool ToBuffer(type::Buffer&) const override { return false; }
    };
    
    // 创建服务器
    UdsServer<SimpleRequest, NonSerializableResponse> server(socket_path_);
    
    // 启动服务器
    server.AsyncResponses([](SimpleRequest const& request, NonSerializableResponse& response) {
        response.id = request.id;
        return kUserRequestSucc;
    });
    
    // 创建客户端并发送请求
    TestClient client(socket_path_);
    SimpleRequest request{1, 0, "Test"};
    SimpleResponse response;
    auto status = client.SendRequest(request, response);
    
    // 验证失败
    EXPECT_NE(status, RequestStatus::kSendAndReceiveSuccess);
}

TEST_F(UdsServerTest, AsyncResponses_CallbackReturnsFailure) {
    // 创建服务器
    UdsServer<SimpleRequest, SimpleResponse> server(socket_path_);
    
    // 启动服务器，回调返回失败
    server.AsyncResponses([](SimpleRequest const& request, SimpleResponse& response) {
        return kUserRequestFail;
    });
    
    // 创建客户端并发送请求
    TestClient client(socket_path_);
    SimpleRequest request{1, 0, "Test"};
    SimpleResponse response;
    auto status = client.SendRequest(request, response);
    
    // 验证客户端收到失败状态
    EXPECT_EQ(status, RequestStatus::kServerProcessFail);
}